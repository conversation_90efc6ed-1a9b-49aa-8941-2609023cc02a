/////////////////////////////////////////////////////////////////
/// getID3() by <PERSON> <<EMAIL>>               //
//  available at http://getid3.sourceforge.net                 //
//            or https://www.getid3.org                        //
//          also https://github.com/<PERSON><PERSON><PERSON>rich/getID3       //
/////////////////////////////////////////////////////////////////
//                                                             //
// /helperapps/readme.txt - part of getID3()                   //
// List of binary files required under Windows for some        //
// features and/or file formats                                //
// See /readme.txt for more details                            //
//                                                            ///
/////////////////////////////////////////////////////////////////

This directory should contain binaries of various helper applications
that getID3() depends on to handle some file formats under Windows.

The location of this directory is configurable in /getid3/getid3.php
as GETID3_HELPERAPPSDIR

If this directory is empty, or you are missing any files, please
download the latest version of the "getID3()-WindowsSupport" package
from the usual download location (http://getid3.sourceforge.net)



Included files:
=====================================================

Taken from http://www.cygwin.com/
* cygwin1.dll

Taken from http://unxutils.sourceforge.net/
* head.exe

Taken from http://www.vorbis.com/download.psp
* vorbiscomment.exe

Taken from http://flac.sourceforge.net/download.html
* metaflac.exe

Taken from http://www.etree.org/shncom.html
* shorten.exe


/////////////////////////////////////////////////////////////////

Changelog:

2003.12.29:
  * Initial release

2019.07.24:
  * Remove obsolete md5sum.exe, sha1sum.exe, tail.exe
