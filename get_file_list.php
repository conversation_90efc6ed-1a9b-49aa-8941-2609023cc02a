<?php
// Include getID3 library for ID3 tag extraction
require_once('getid3/getid3.php');

$folderPath = $_POST['folderPath'];

$fileList = array();

$allowedExtensions = array('mp3', 'm4a');

$excludedFolders = []; // Initialize an array to hold excluded folders

// Load excluded folders from configuration file
$configData = json_decode(file_get_contents('config.json'), true);
if ($configData && isset($configData['excluded_folders'])) {
    $excludedFolders = $configData['excluded_folders'];
}

// Initialize getID3 engine
$getID3 = new getID3;
$getID3->setOption(array('encoding' => 'UTF-8'));

$iterator = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($folderPath), RecursiveIteratorIterator::SELF_FIRST);
foreach ($iterator as $file) {
    if ($file->isFile() && in_array($file->getExtension(), $allowedExtensions)) {
        // Check if the file's directory is excluded
        $filePath = $file->getPathname();
        $relativePath = substr($filePath, strlen($folderPath) + 1);
        $relativePathParts = explode('/', $relativePath);
        $folderToCheck = $relativePathParts[0];

        if (!in_array($folderToCheck, $excludedFolders)) {
            // Extract ID3 tags
            $fileInfo = $getID3->analyze($filePath);
            getid3_lib::CopyTagsToComments($fileInfo);

            // Extract tag information with fallbacks
            $title = '';
            $artist = '';
            $album = '';
            $year = '';
            $track = '';
            $albumArt = null;

            // Try to get tags from various sources
            if (isset($fileInfo['comments'])) {
                $title = isset($fileInfo['comments']['title'][0]) ? $fileInfo['comments']['title'][0] : '';
                $artist = isset($fileInfo['comments']['artist'][0]) ? $fileInfo['comments']['artist'][0] : '';
                $album = isset($fileInfo['comments']['album'][0]) ? $fileInfo['comments']['album'][0] : '';
                $year = isset($fileInfo['comments']['year'][0]) ? $fileInfo['comments']['year'][0] : '';
                $track = isset($fileInfo['comments']['track_number'][0]) ? $fileInfo['comments']['track_number'][0] : '';
            }

            // Fallback to ID3v2 tags if comments are empty
            if (empty($title) && isset($fileInfo['tags']['id3v2']['title'][0])) {
                $title = $fileInfo['tags']['id3v2']['title'][0];
            }
            if (empty($artist) && isset($fileInfo['tags']['id3v2']['artist'][0])) {
                $artist = $fileInfo['tags']['id3v2']['artist'][0];
            }
            if (empty($album) && isset($fileInfo['tags']['id3v2']['album'][0])) {
                $album = $fileInfo['tags']['id3v2']['album'][0];
            }
            if (empty($year) && isset($fileInfo['tags']['id3v2']['year'][0])) {
                $year = $fileInfo['tags']['id3v2']['year'][0];
            }
            if (empty($track) && isset($fileInfo['tags']['id3v2']['track_number'][0])) {
                $track = $fileInfo['tags']['id3v2']['track_number'][0];
            }

            // Extract album art
            if (isset($fileInfo['comments']['picture'][0])) {
                $picture = $fileInfo['comments']['picture'][0];
                $albumArt = array(
                    'data' => base64_encode($picture['data']),
                    'image_mime' => $picture['image_mime'],
                    'picturetype' => $picture['picturetype'] ?? 'Cover (front)',
                    'description' => $picture['description'] ?? ''
                );
            }

            // Use filename without extension as fallback for title
            if (empty($title)) {
                $title = pathinfo($file->getFilename(), PATHINFO_FILENAME);
            }

            $fileList[] = array(
                'name' => $file->getFilename(),
                'path' => $filePath,
                'id3' => array(
                    'title' => $title,
                    'artist' => $artist,
                    'album' => $album,
                    'year' => $year,
                    'track' => $track,
                    'picture' => $albumArt
                )
            );
        }
    }
}

// Save the file list to a JSON file
$jsonFilePath = 'file_list.json';
file_put_contents($jsonFilePath, json_encode($fileList, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES | JSON_NUMERIC_CHECK));

echo json_encode($fileList);
?>
