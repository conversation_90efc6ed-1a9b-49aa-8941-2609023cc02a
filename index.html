<!DOCTYPE html>
<html>

<head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>CMP Media Player</title>
    <script src="./assets/js/jquery-3.7.1.min.js"></script>
    <script src="./assets/js/search.min.js"></script>
    <script src="./assets/js/tag.reader.min.js"></script>
    <script src="./assets/js/cmp.player.min.js"></script>
    <script src="./assets/js/jsmediatags.min.js"></script>

    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/fomantic-ui@2.9.2/dist/semantic.min.css">
    <script src="https://cdn.jsdelivr.net/npm/fomantic-ui@2.9.2/dist/semantic.min.js"></script>

    <!-- Bulma CSS for mobile buttons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">

    <style>
        * {
            box-sizing: border-box;
        }

        body {
            background: linear-gradient(180deg, #121212 0%, #000000 100%);
            margin: 0;
            padding: 0;
            font-family: 'Circular', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            color: #ffffff;
            overflow-x: hidden;
            height: 100vh;
        }

        /* Mobile-first Spotify-like layout */
        .mobile-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            background: linear-gradient(180deg, #121212 0%, #000000 100%);
        }

        /* Top navigation bar */
        .top-nav {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 16px;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .nav-title {
            font-size: 18px;
            font-weight: 700;
            color: #ffffff;
        }

        .nav-icon {
            width: 24px;
            height: 24px;
            cursor: pointer;
            color: #b3b3b3;
        }

        /* Main content area */
        .main-content {
            flex: 1;
            overflow-y: auto;
            padding-bottom: 100px;
        }

        /* Now Playing Screen */
        .now-playing {
            display: none;
            flex-direction: column;
            height: 100vh;
            background: linear-gradient(180deg, #1db954 0%, #121212 50%);
            position: relative;
        }

        .now-playing.active {
            display: flex;
        }

        .now-playing-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            z-index: 10;
            background: rgba(0, 0, 0, 0.3);
        }

        .back-btn {
            width: 24px;
            height: 24px;
            cursor: pointer;
            color: #ffffff;
        }

        .album-art-container {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 80px 32px 32px;
        }

        .album-art {
            width: 100%;
            max-width: 350px;
            aspect-ratio: 1;
            border-radius: 8px;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.5);
            object-fit: cover;
        }

        .track-info {
            padding: 0 32px 16px;
            text-align: left;
        }

        .track-title {
            font-size: 24px;
            font-weight: 700;
            color: #ffffff;
            margin-bottom: 8px;
            line-height: 1.2;
        }

        .track-artist {
            font-size: 16px;
            color: #b3b3b3;
            font-weight: 400;
        }

        /* Player controls */
        .player-controls {
            padding: 16px 32px 32px;
        }

        .progress-container {
            margin-bottom: 24px;
        }

        .progress-bar {
            width: 100%;
            height: 4px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 2px;
            position: relative;
            cursor: pointer;
        }

        .progress-fill {
            height: 100%;
            background: #1db954;
            border-radius: 2px;
            width: 0%;
            transition: width 0.1s;
        }

        .time-display {
            display: flex;
            justify-content: space-between;
            margin-top: 8px;
            font-size: 12px;
            color: #b3b3b3;
        }

        .control-buttons {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 32px;
        }

        /* Bulma button overrides for mobile player */
        .mobile-container .button {
            background: transparent;
            border: none;
            color: #ffffff;
            cursor: pointer;
            padding: 8px;
            border-radius: 50%;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 44px;
            min-width: 44px;
        }

        .mobile-container .button:hover {
            background: rgba(255, 255, 255, 0.1);
            color: #ffffff;
            transform: scale(1.05);
        }

        .mobile-container .button:active {
            transform: scale(0.95);
        }

        .mobile-container .button:focus {
            box-shadow: 0 0 0 0.125em rgba(29, 185, 84, 0.25);
        }

        .mobile-container .play-pause-btn {
            width: 64px;
            height: 64px;
            background: #1db954;
            color: #000000;
            border-radius: 50%;
            font-size: 24px;
            box-shadow: 0 4px 12px rgba(29, 185, 84, 0.3);
        }

        .mobile-container .play-pause-btn:hover {
            background: #1ed760;
            transform: scale(1.05);
            box-shadow: 0 6px 16px rgba(29, 185, 84, 0.4);
        }

        .mobile-container .play-pause-btn:active {
            transform: scale(0.95);
            background: #1aa34a;
        }

        .mobile-container .skip-btn {
            font-size: 24px;
        }

        .mobile-container .mini-play-btn {
            background: transparent;
            border: none;
            color: #ffffff;
            font-size: 20px;
            cursor: pointer;
            padding: 8px;
            border-radius: 50%;
            transition: all 0.2s ease;
            min-height: 44px;
            min-width: 44px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .mobile-container .mini-play-btn:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: scale(1.05);
        }

        /* Additional Bulma button enhancements */
        .mobile-container .button.is-rounded {
            border-radius: 50% !important;
        }

        .mobile-container .button:not(.play-pause-btn) {
            background: rgba(255, 255, 255, 0.05) !important;
            border: 1px solid rgba(255, 255, 255, 0.1) !important;
        }

        .mobile-container .button:not(.play-pause-btn):hover {
            background: rgba(255, 255, 255, 0.15) !important;
            border-color: rgba(255, 255, 255, 0.2) !important;
        }

        /* Spotify green accent for active states */
        .mobile-container .button.is-active,
        .mobile-container .button.is-focused {
            background: rgba(29, 185, 84, 0.2) !important;
            border-color: #1db954 !important;
            color: #1db954 !important;
        }

        /* Loading state for buttons */
        .mobile-container .button.is-loading::after {
            border-color: transparent transparent #1db954 #1db954 !important;
        }

        /* Small navigation buttons */
        .mobile-container .button.is-small {
            height: 32px !important;
            width: 32px !important;
            padding: 0 !important;
            font-size: 14px;
        }

        .mobile-container .nav-icon,
        .mobile-container .back-btn {
            background: rgba(0, 0, 0, 0.3) !important;
            border: none !important;
            color: #ffffff !important;
        }

        .mobile-container .nav-icon:hover,
        .mobile-container .back-btn:hover {
            background: rgba(0, 0, 0, 0.5) !important;
            transform: scale(1.1);
        }

        /* Playlist view */
        .playlist-view {
            padding: 16px;
        }

        .search-container {
            margin-bottom: 24px;
        }

        .search-input {
            width: 100%;
            padding: 12px 16px;
            background: rgba(255, 255, 255, 0.1);
            border: none;
            border-radius: 8px;
            color: #ffffff;
            font-size: 16px;
        }

        .search-input::placeholder {
            color: #b3b3b3;
        }

        .track-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .track-item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            cursor: pointer;
            border-radius: 8px;
            transition: background 0.2s;
        }

        .track-item:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .track-item.playing {
            background: rgba(29, 185, 84, 0.2);
        }

        .track-thumbnail {
            width: 56px;
            height: 56px;
            border-radius: 4px;
            margin-right: 12px;
            object-fit: cover;
            background: #333;
        }

        .track-details {
            flex: 1;
            min-width: 0;
        }

        .track-name {
            font-size: 16px;
            font-weight: 500;
            color: #ffffff;
            margin-bottom: 4px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .track-meta {
            font-size: 14px;
            color: #b3b3b3;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* Bottom mini player */
        .mini-player {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(40, 40, 40, 0.95);
            backdrop-filter: blur(10px);
            padding: 12px 16px;
            display: flex;
            align-items: center;
            gap: 12px;
            cursor: pointer;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .mini-player.hidden {
            display: none;
        }

        .mini-album-art {
            width: 48px;
            height: 48px;
            border-radius: 4px;
            object-fit: cover;
        }

        .mini-track-info {
            flex: 1;
            min-width: 0;
        }

        .mini-track-title {
            font-size: 14px;
            font-weight: 500;
            color: #ffffff;
            margin-bottom: 2px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .mini-track-artist {
            font-size: 12px;
            color: #b3b3b3;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .mini-play-btn {
            background: none;
            border: none;
            color: #ffffff;
            font-size: 20px;
            cursor: pointer;
            padding: 8px;
        }

        /* Hide desktop elements on mobile */
        @media (max-width: 768px) {
            .ui.container,
            .ui.grid,
            .ui.segment,
            #audioVisualizer {
                display: none !important;
            }

            .ui.bottom.fixed.menu {
                display: none !important;
            }
        }

        /* Show mobile layout only on mobile */
        @media (min-width: 769px) {
            .mobile-container {
                display: none;
            }
        }

        /* Desktop styles (when mobile is hidden) */
        @media (min-width: 769px) {
            body {
                background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)),
                    url('./assets/images/bg2_up.jpg') no-repeat center/cover fixed;
                min-height: 100vh;
                padding-bottom: 80px;
                animation: fadeIn 1.5s ease-out;
            }

            @keyframes fadeIn {
                0% {
                    opacity: 0;
                    transform: translateY(20px);
                }
                100% {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            .ui.segment {
                background: rgba(27, 28, 29, 0.8);
                color: #fff;
                border: none;
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
                backdrop-filter: blur(10px);
            }

            .ui.list .item {
                color: #fff;
            }

            .ui.list .header {
                color: #24add6;
            }

            .ui.list .description,
            .description,
            #title,
            #artist,
            #album,
            #year,
            #track {
                color: #fff !important;
                margin-top: 0.5rem;
                font-size: 1.1em;
                font-weight: 500;
            }

            .ui.list .item {
                padding: 0.8em 0;
            }

            .ui.horizontal.label {
                min-width: 80px;
                text-align: center;
            }

            .ui.divider {
                border-top: 1px solid rgba(255, 255, 255, 0.1);
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            }

            #playlist-container {
                max-height: 550px;
                overflow-y: auto;
            }

            #playlist-container::-webkit-scrollbar {
                width: 10px;
                background: transparent;
            }

            #playlist-container::-webkit-scrollbar-thumb {
                background: #ff0000;
                border-radius: 5px;
            }

            #playlist-container::-webkit-scrollbar-track {
                background: transparent;
            }

            #playlist-container {
                scrollbar-width: thin;
                scrollbar-color: #792626 transparent;
            }

            .playing {
                background: linear-gradient(90deg, rgba(36, 173, 214, 0.2), rgba(36, 173, 214, 0.1));
                color: #fff;
                font-weight: bold;
                border-left: 3px solid #24add6;
            }

            .completed {
                opacity: 0.6;
                font-style: italic;
            }

            .build-time-message {
                color: #24add6;
                text-align: center;
                margin: 1rem 0;
                font-weight: 500;
            }

            .ui.relaxed.divided.list .item:not(.playing):not(.completed):hover {
                background: linear-gradient(90deg, rgba(36, 173, 214, 0.12), rgba(36, 173, 214, 0.05));
                color: #24add6;
                cursor: pointer;
                transition: background 0.2s, color 0.2s;
            }
        }

        /* Simple text-based icons that work everywhere */
        .icon-back::before {
            content: "<";
            font-size: 1.5em;
            font-weight: bold;
        }

        .icon-menu::before {
            content: "☰";
            font-size: 1.2em;
        }

        .icon-search::before {
            content: "🔍";
            font-size: 1.1em;
        }

        /* Play icon using CSS triangle */
        .icon-play {
            position: relative;
        }
        .icon-play::before {
            content: "";
            width: 0;
            height: 0;
            border-left: 10px solid currentColor;
            border-top: 7px solid transparent;
            border-bottom: 7px solid transparent;
            display: inline-block;
            margin-left: 3px;
        }

        /* Pause icon using CSS bars */
        .icon-pause {
            position: relative;
        }
        .icon-pause::before {
            content: "";
            width: 4px;
            height: 14px;
            background: currentColor;
            display: inline-block;
            box-shadow: 6px 0 0 currentColor;
        }

        .icon-skip-prev::before {
            content: "⏮";
            font-size: 1.2em;
        }

        .icon-skip-next::before {
            content: "⏭";
            font-size: 1.2em;
        }

        .icon-shuffle::before {
            content: "🔀";
            font-size: 1.1em;
        }

        .icon-repeat::before {
            content: "🔁";
            font-size: 1.1em;
        }

        /* Fallback for devices that don't support emoji */
        @supports not (content: "🔍") {
            .icon-search::before { content: "S"; }
            .icon-skip-prev::before { content: "<<"; }
            .icon-skip-next::before { content: ">>"; }
            .icon-shuffle::before { content: "SH"; }
            .icon-repeat::before { content: "RP"; }
        }

        /* Smooth transitions */
        .track-item, .control-btn, .mini-play-btn {
            transition: all 0.2s ease;
        }

        /* Touch improvements */
        @media (max-width: 768px) {
            .control-btn, .mini-play-btn {
                min-height: 44px;
                min-width: 44px;
            }

            .track-item {
                min-height: 60px;
            }
        }
    </style>

</head>

<body>
    <!-- Desktop Layout (existing) -->
    <div class="ui container">
        <h1 class="ui center aligned header" style="margin-top: 2rem; color: #24add6;">
            <i class="music icon"></i>
            <div class="content">Playlist</div>
        </h1>

        <div class="ui fluid action input" style="margin: 2rem 0;">
            <input type="text" id="searchInput" placeholder="Search songs...">
            <button id="rebuildPlaylistBtn" class="ui teal right labeled icon button">
                <i class="sync icon"></i>
                Rebuild Playlist
            </button>
        </div>

        <div class="ui grid">
            <div class="six wide column">
                <div class="ui segment" id="id3-details">
                    <h3 class="ui center aligned header" style="color: #24add6; margin-bottom: 1.5rem;">
                        <i class="info circle icon"></i>
                        <div class="content">Song Details</div>
                    </h3>
                    <div class="ui divided list">
                        <div class="item">
                            <div class="ui teal horizontal label">Title</div>
                            <div id="title" class="description"></div>
                        </div>
                        <div class="item">
                            <div class="ui teal horizontal label">Artist</div>
                            <div id="artist" class="description"></div>
                        </div>
                        <div class="item">
                            <div class="ui teal horizontal label">Album</div>
                            <div id="album" class="description"></div>
                        </div>
                        <div class="item">
                            <div class="ui teal horizontal label">Year</div>
                            <div id="year" class="description"></div>
                        </div>
                        <div class="item">
                            <div class="ui teal horizontal label">Track</div>
                            <div id="track" class="description"></div>
                        </div>
                    </div>
                    <div class="ui divider"></div>
                    <div class="ui centered image">
                        <img id="picture" class="ui rounded image" src="./assets/images/cover.png" width="200px"
                            alt="Album cover" />
                    </div>
                    <canvas id="audioVisualizer" width="200" height="200" style="margin-top: 1rem;"></canvas>
                </div>
            </div>

            <div class="ten wide column">
                <div class="ui segment" id="playlist-container">
                    <h3 class="ui center aligned header" style="color: #24add6; margin-bottom: 1.5rem;">
                        <i class="list icon"></i>
                        <div class="content">Music Library</div>
                    </h3>
                    <div class="ui relaxed divided list" id="fileList"></div>
                </div>
            </div>
        </div>
    </div>

    <div class="ui bottom fixed menu" style="background: transparent; border: none; box-shadow: none; padding: 1rem;">
        <div class="ui container">
            <audio id="mainAudioPlayer" controls class="ui fluid" style="width: 100%;"></audio>
        </div>
    </div>

    <!-- Mobile Layout (Spotify-like) -->
    <div class="mobile-container">
        <!-- Now Playing Screen -->
        <div class="now-playing" id="nowPlayingScreen">
            <div class="now-playing-header">
                <button class="button is-rounded is-small back-btn icon-back" id="backToPlaylist" title="Back"></button>
                <div class="nav-title has-text-weight-bold">Now Playing</div>
                <button class="button is-rounded is-small nav-icon icon-menu" title="Menu"></button>
            </div>

            <div class="album-art-container">
                <img id="mobileAlbumArt" class="album-art" src="./assets/images/cover.png" alt="Album Art">
            </div>

            <div class="track-info">
                <div class="track-title has-text-weight-bold is-size-4" id="mobileTrackTitle">Select a song</div>
                <div class="track-artist has-text-grey-light is-size-5" id="mobileTrackArtist">Unknown Artist</div>
            </div>

            <div class="player-controls">
                <div class="progress-container">
                    <div class="progress-bar" id="progressBar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="time-display">
                        <span id="currentTime">0:00</span>
                        <span id="totalTime">0:00</span>
                    </div>
                </div>

                <div class="control-buttons">
                    <button class="button is-rounded icon-shuffle" id="shuffleBtn" title="Shuffle"></button>
                    <button class="button is-rounded skip-btn icon-skip-prev" id="prevBtn" title="Previous"></button>
                    <button class="button is-rounded play-pause-btn" id="playPauseBtn" title="Play/Pause">
                        <span class="icon-play"></span>
                    </button>
                    <button class="button is-rounded skip-btn icon-skip-next" id="nextBtn" title="Next"></button>
                    <button class="button is-rounded icon-repeat" id="repeatBtn" title="Repeat"></button>
                </div>
            </div>
        </div>

        <!-- Playlist Screen -->
        <div class="playlist-screen" id="playlistScreen">
            <div class="top-nav">
                <button class="button is-rounded is-small nav-icon icon-menu" title="Menu"></button>
                <div class="nav-title has-text-weight-bold">Your Library</div>
                <button class="button is-rounded is-small nav-icon icon-search" id="searchToggle" title="Search"></button>
            </div>

            <div class="main-content">
                <div class="playlist-view">
                    <div class="search-container" id="searchContainer" style="display: none;">
                        <input type="text" class="search-input" id="mobileSearchInput" placeholder="Search your library">
                    </div>

                    <div class="track-list" id="mobileTrackList">
                        <!-- Tracks will be populated here -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Mini Player -->
        <div class="mini-player hidden" id="miniPlayer">
            <img id="miniAlbumArt" class="mini-album-art" src="./assets/images/cover.png" alt="Album Art">
            <div class="mini-track-info">
                <div class="mini-track-title" id="miniTrackTitle">No song playing</div>
                <div class="mini-track-artist" id="miniTrackArtist">Unknown Artist</div>
            </div>
            <button class="button is-rounded mini-play-btn" id="miniPlayBtn">
                <span class="icon-play"></span>
            </button>
        </div>
    </div>

    <script>
        // Desktop visualization (only for desktop)
        if (window.innerWidth > 768) {
            var canvas = document.getElementById('audioVisualizer');
            if (canvas) {
                var ctx = canvas.getContext('2d');
                var audioContext = null;
                var audioSrc = null;
                var analyser = null;
                var drawAnimationId = null;

                function visualizeAudio() {
                    var audioElement = document.getElementById('mainAudioPlayer');

                    if (!audioContext) {
                        audioContext = new (window.AudioContext || window.webkitAudioContext)();
                        audioSrc = audioContext.createMediaElementSource(audioElement);
                        analyser = audioContext.createAnalyser();
                        analyser.fftSize = 256;
                        audioSrc.connect(analyser);
                        audioSrc.connect(audioContext.destination);
                    }

                    var bufferLength = analyser.frequencyBinCount;
                    var dataArray = new Uint8Array(bufferLength);

                    if (drawAnimationId) {
                        cancelAnimationFrame(drawAnimationId);
                    }

                    function draw() {
                        ctx.clearRect(0, 0, canvas.width, canvas.height);
                        analyser.getByteFrequencyData(dataArray);
                        var barWidth = canvas.width / bufferLength;
                        var barHeight;
                        var x = 0;
                        for (var i = 0; i < bufferLength; i++) {
                            barHeight = dataArray[i] / 2;
                            ctx.fillStyle = 'rgb(' + (barHeight + 100) + ',50,50)';
                            ctx.fillRect(x, canvas.height - barHeight, barWidth, barHeight);
                            x += barWidth + 1;
                        }
                        drawAnimationId = requestAnimationFrame(draw);
                    }
                    draw();
                }

                document.getElementById('mainAudioPlayer').addEventListener('play', visualizeAudio);
            }
        }

        // Mobile interface JavaScript
        $(document).ready(function() {
            var isMobile = window.innerWidth <= 768;
            var currentTrack = null;
            var isPlaying = false;
            var tracks = [];

            if (isMobile) {
                initializeMobileInterface();
            }

            function initializeMobileInterface() {
                // Hide desktop search and use mobile search
                $('#searchInput').hide();

                // Mobile search toggle
                $('#searchToggle').on('click', function() {
                    $('#searchContainer').toggle();
                    if ($('#searchContainer').is(':visible')) {
                        $('#mobileSearchInput').focus();
                    }
                });

                // Mobile search functionality
                $('#mobileSearchInput').on('input', function() {
                    var searchTerm = $(this).val().toLowerCase();
                    filterMobileTracks(searchTerm);
                });

                // Back to playlist button
                $('#backToPlaylist').on('click', function() {
                    showPlaylistScreen();
                });

                // Mini player click to show now playing
                $('#miniPlayer').on('click', function() {
                    showNowPlayingScreen();
                });

                // Play/pause buttons
                $('#playPauseBtn, #miniPlayBtn').on('click', function(e) {
                    e.stopPropagation();
                    togglePlayPause();
                });

                // Previous/Next buttons
                $('#prevBtn').on('click', function() {
                    playPreviousTrack();
                });

                $('#nextBtn').on('click', function() {
                    playNextTrack();
                });

                // Progress bar interaction
                $('#progressBar').on('click', function(e) {
                    var audio = document.getElementById('mainAudioPlayer');
                    var rect = this.getBoundingClientRect();
                    var percent = (e.clientX - rect.left) / rect.width;
                    audio.currentTime = percent * audio.duration;
                });

                // Audio event listeners
                var audio = document.getElementById('mainAudioPlayer');
                audio.addEventListener('timeupdate', updateProgress);
                audio.addEventListener('loadedmetadata', updateDuration);
                audio.addEventListener('play', function() {
                    isPlaying = true;
                    updatePlayButtons();
                    showMiniPlayer();
                });
                audio.addEventListener('pause', function() {
                    isPlaying = false;
                    updatePlayButtons();
                });
                audio.addEventListener('ended', function() {
                    playNextTrack();
                });

                // Load tracks into mobile interface
                loadMobileTracks();
            }

            function loadMobileTracks() {
                // Wait for the desktop playlist to load, then convert to mobile
                setTimeout(function() {
                    var desktopTracks = $('#fileList a');
                    tracks = [];

                    desktopTracks.each(function(index) {
                        var $this = $(this);
                        var href = $this.attr('href');
                        var text = $this.text().trim();

                        tracks.push({
                            index: index,
                            href: href,
                            name: text,
                            element: $this
                        });
                    });

                    renderMobileTracks();
                }, 1000);
            }

            function renderMobileTracks() {
                var $mobileList = $('#mobileTrackList');
                $mobileList.empty();

                tracks.forEach(function(track, index) {
                    var $trackItem = $('<div class="track-item" data-index="' + index + '">');

                    var $thumbnail = $('<img class="track-thumbnail" src="./assets/images/cover.png" alt="Track">');
                    var $details = $('<div class="track-details">');
                    var $name = $('<div class="track-name">').text(track.name);
                    var $meta = $('<div class="track-meta">').text('Unknown Artist');

                    $details.append($name, $meta);
                    $trackItem.append($thumbnail, $details);

                    $trackItem.on('click', function() {
                        playTrack(index);
                        showNowPlayingScreen();
                    });

                    $mobileList.append($trackItem);
                });
            }

            function filterMobileTracks(searchTerm) {
                $('.track-item').each(function() {
                    var trackName = $(this).find('.track-name').text().toLowerCase();
                    if (trackName.includes(searchTerm)) {
                        $(this).show();
                    } else {
                        $(this).hide();
                    }
                });
            }

            function playTrack(index) {
                if (tracks[index]) {
                    currentTrack = index;
                    var track = tracks[index];

                    // Update audio source
                    $('#mainAudioPlayer').attr('src', track.href);
                    $('#mainAudioPlayer')[0].play();

                    // Update mobile UI
                    updateMobileTrackInfo(track.name);
                    updateActiveTrack();

                    // Trigger desktop tag reading
                    if (window.showTags) {
                        showTags(track.href);
                    }
                }
            }

            function updateMobileTrackInfo(trackName) {
                $('#mobileTrackTitle, #miniTrackTitle').text(trackName);
                // Artist info will be updated by the tag reader
            }

            function updateActiveTrack() {
                $('.track-item').removeClass('playing');
                if (currentTrack !== null) {
                    $('.track-item[data-index="' + currentTrack + '"]').addClass('playing');
                }
            }

            function togglePlayPause() {
                var audio = document.getElementById('mainAudioPlayer');
                if (isPlaying) {
                    audio.pause();
                } else {
                    audio.play();
                }
            }

            function playPreviousTrack() {
                if (currentTrack !== null && currentTrack > 0) {
                    playTrack(currentTrack - 1);
                }
            }

            function playNextTrack() {
                if (currentTrack !== null && currentTrack < tracks.length - 1) {
                    playTrack(currentTrack + 1);
                }
            }

            function updatePlayButtons() {
                var playIcon = isPlaying ? 'icon-pause' : 'icon-play';
                $('#playPauseBtn span, #miniPlayBtn span').removeClass('icon-play icon-pause').addClass(playIcon);
            }

            function updateProgress() {
                var audio = document.getElementById('mainAudioPlayer');
                if (audio.duration) {
                    var percent = (audio.currentTime / audio.duration) * 100;
                    $('#progressFill').css('width', percent + '%');
                    $('#currentTime').text(formatTime(audio.currentTime));
                }
            }

            function updateDuration() {
                var audio = document.getElementById('mainAudioPlayer');
                $('#totalTime').text(formatTime(audio.duration));
            }

            function formatTime(seconds) {
                var minutes = Math.floor(seconds / 60);
                var secs = Math.floor(seconds % 60);
                return minutes + ':' + (secs < 10 ? '0' : '') + secs;
            }

            function showNowPlayingScreen() {
                $('#nowPlayingScreen').addClass('active');
                $('#playlistScreen').hide();
                $('#miniPlayer').addClass('hidden');
            }

            function showPlaylistScreen() {
                $('#nowPlayingScreen').removeClass('active');
                $('#playlistScreen').show();
                if (currentTrack !== null) {
                    $('#miniPlayer').removeClass('hidden');
                }
            }

            function showMiniPlayer() {
                if (currentTrack !== null && !$('#nowPlayingScreen').hasClass('active')) {
                    $('#miniPlayer').removeClass('hidden');
                }
            }

            // Override the desktop tag reader for mobile
            if (isMobile && window.showTags) {
                var originalShowTags = window.showTags;
                window.showTags = function(src) {
                    originalShowTags(src);

                    // Update mobile interface with tag info
                    setTimeout(function() {
                        var title = $('#title').text() || 'Unknown Title';
                        var artist = $('#artist').text() || 'Unknown Artist';
                        var albumArt = $('#picture').attr('src');

                        $('#mobileTrackTitle, #miniTrackTitle').text(title);
                        $('#mobileTrackArtist, #miniTrackArtist').text(artist);
                        $('#mobileAlbumArt, #miniAlbumArt').attr('src', albumArt);
                    }, 100);
                };
            }

            // Handle window resize
            $(window).on('resize', function() {
                if (window.innerWidth <= 768 && !isMobile) {
                    location.reload(); // Reload to switch to mobile
                } else if (window.innerWidth > 768 && isMobile) {
                    location.reload(); // Reload to switch to desktop
                }
            });
        });
    </script>

</body>

</html>