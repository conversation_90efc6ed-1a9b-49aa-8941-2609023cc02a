<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>Mobile Music Player Demo</title>
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            background: linear-gradient(180deg, #121212 0%, #000000 100%);
            font-family: 'Circular', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            color: #ffffff;
            overflow-x: hidden;
            height: 100vh;
        }

        .mobile-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
        }

        .top-nav {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 16px;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
        }

        .nav-title {
            font-size: 18px;
            font-weight: 700;
            color: #ffffff;
        }

        .nav-icon {
            width: 24px;
            height: 24px;
            cursor: pointer;
            color: #b3b3b3;
        }

        .now-playing {
            display: flex;
            flex-direction: column;
            height: 100vh;
            background: linear-gradient(180deg, #1db954 0%, #121212 50%);
            position: relative;
        }

        .now-playing-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            z-index: 10;
            background: rgba(0, 0, 0, 0.3);
        }

        .back-btn {
            width: 24px;
            height: 24px;
            cursor: pointer;
            color: #ffffff;
        }

        .album-art-container {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 80px 32px 32px;
        }

        .album-art {
            width: 100%;
            max-width: 350px;
            aspect-ratio: 1;
            border-radius: 8px;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.5);
            object-fit: cover;
        }

        .track-info {
            padding: 0 32px 16px;
            text-align: left;
        }

        .track-title {
            font-size: 24px;
            font-weight: 700;
            color: #ffffff;
            margin-bottom: 8px;
            line-height: 1.2;
        }

        .track-artist {
            font-size: 16px;
            color: #b3b3b3;
            font-weight: 400;
        }

        .player-controls {
            padding: 16px 32px 32px;
        }

        .progress-container {
            margin-bottom: 24px;
        }

        .progress-bar {
            width: 100%;
            height: 4px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 2px;
            position: relative;
            cursor: pointer;
        }

        .progress-fill {
            height: 100%;
            background: #1db954;
            border-radius: 2px;
            width: 30%;
            transition: width 0.1s;
        }

        .time-display {
            display: flex;
            justify-content: space-between;
            margin-top: 8px;
            font-size: 12px;
            color: #b3b3b3;
        }

        .control-buttons {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 32px;
        }

        .control-btn {
            background: none;
            border: none;
            color: #ffffff;
            cursor: pointer;
            padding: 8px;
            border-radius: 50%;
            transition: transform 0.1s;
            min-height: 44px;
            min-width: 44px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .control-btn:active {
            transform: scale(0.95);
        }

        .play-pause-btn {
            width: 64px;
            height: 64px;
            background: #ffffff;
            color: #000000;
            border-radius: 50%;
            font-size: 24px;
        }

        .skip-btn {
            font-size: 24px;
        }

        /* Icons */
        .icon-back::before { content: "←"; }
        .icon-menu::before { content: "☰"; }
        .icon-play::before { content: "▶"; }
        .icon-pause::before { content: "⏸"; }
        .icon-skip-prev::before { content: "⏮"; }
        .icon-skip-next::before { content: "⏭"; }
        .icon-shuffle::before { content: "🔀"; }
        .icon-repeat::before { content: "🔁"; }
    </style>
</head>
<body>
    <div class="mobile-container">
        <div class="now-playing">
            <div class="now-playing-header">
                <div class="back-btn icon-back"></div>
                <div class="nav-title">Now Playing</div>
                <div class="nav-icon icon-menu"></div>
            </div>
            
            <div class="album-art-container">
                <img class="album-art" src="./assets/images/cover.png" alt="Album Art">
            </div>
            
            <div class="track-info">
                <div class="track-title">Bohemian Rhapsody</div>
                <div class="track-artist">Queen</div>
            </div>
            
            <div class="player-controls">
                <div class="progress-container">
                    <div class="progress-bar">
                        <div class="progress-fill"></div>
                    </div>
                    <div class="time-display">
                        <span>1:23</span>
                        <span>5:55</span>
                    </div>
                </div>
                
                <div class="control-buttons">
                    <button class="control-btn icon-shuffle"></button>
                    <button class="control-btn skip-btn icon-skip-prev"></button>
                    <button class="control-btn play-pause-btn">
                        <span class="icon-pause"></span>
                    </button>
                    <button class="control-btn skip-btn icon-skip-next"></button>
                    <button class="control-btn icon-repeat"></button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Demo functionality
        document.querySelector('.play-pause-btn').addEventListener('click', function() {
            const icon = this.querySelector('span');
            if (icon.classList.contains('icon-pause')) {
                icon.className = 'icon-play';
            } else {
                icon.className = 'icon-pause';
            }
        });

        // Demo progress animation
        let progress = 30;
        setInterval(() => {
            progress += 0.1;
            if (progress > 100) progress = 0;
            document.querySelector('.progress-fill').style.width = progress + '%';
            
            const currentMinutes = Math.floor((progress / 100) * 355 / 60);
            const currentSeconds = Math.floor((progress / 100) * 355 % 60);
            document.querySelector('.time-display span:first-child').textContent = 
                currentMinutes + ':' + (currentSeconds < 10 ? '0' : '') + currentSeconds;
        }, 100);
    </script>
</body>
</html>
